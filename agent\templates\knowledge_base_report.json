{"id": 20, "title": "Report Agent Using Knowledge Base", "description": "A report generation assistant using local knowledge base, with advanced capabilities in task planning, reasoning, and reflective analysis. Recommended for academic research paper Q&A", "canvas_type": "Agent", "dsl": {"components": {"Agent:NewPumasLick": {"downstream": ["Message:OrangeYearsShine"], "obj": {"component_name": "Agent", "params": {"delay_after_error": 1, "description": "", "exception_comment": "", "exception_default_value": "", "exception_goto": [], "exception_method": null, "frequencyPenaltyEnabled": false, "frequency_penalty": 0.5, "llm_id": "qwen3-235b-a22b-instruct-2507@Tongyi-Qianwen", "maxTokensEnabled": true, "max_retries": 3, "max_rounds": 3, "max_tokens": 128000, "mcp": [], "message_history_window_size": 12, "outputs": {"content": {"type": "string", "value": ""}}, "parameter": "Precise", "presencePenaltyEnabled": false, "presence_penalty": 0.5, "prompts": [{"content": "# User Query\n {sys.query}", "role": "user"}], "sys_prompt": "## Role & Task\nYou are a **“Knowledge Base Retrieval Q\\&A Agent”** whose goal is to break down the user’s question into retrievable subtasks, and then produce a multi-source-verified, structured, and actionable research report using the internal knowledge base.\n## Execution Framework (Detailed Steps & Key Points)\n1. **Assessment & Decomposition**\n   * Actions:\n     * Automatically extract: main topic, subtopics, entities (people/organizations/products/technologies), time window, geographic/business scope.\n     * Output as a list: N facts/data points that must be collected (*N* ranges from 5–20 depending on question complexity).\n2. **Query Type Determination (Rule-Based)**\n   * Example rules:\n     * If the question involves a single issue but requests “method comparison/multiple explanations” → use **depth-first**.\n     * If the question can naturally be split into ≥3 independent sub-questions → use **breadth-first**.\n     * If the question can be answered by a single fact/specification/definition → use **simple query**.\n3. **Research Plan Formulation**\n   * Depth-first: define 3–5 perspectives (methodology/stakeholders/time dimension/technical route, etc.), assign search keywords, target document types, and output format for each perspective.\n   * Breadth-first: list subtasks, prioritize them, and assign search terms.\n   * Simple query: directly provide the search sentence and required fields.\n4. **Retrieval Execution**\n   * After retrieval: perform coverage check (does it contain the key facts?) and quality check (source diversity, authority, latest update time).\n   * If standards are not met, automatically loop: rewrite queries (synonyms/cross-domain terms) and retry ≤3 times, or flag as requiring external search.\n5. **Integration & Reasoning**\n   * Build the answer using a **fact–evidence–reasoning** chain. For each conclusion, attach 1–2 strongest pieces of evidence.\n---\n## Quality Gate Checklist (Verify at Each Stage)\n* **Stage 1 (Decomposition)**:\n  * [ ] Key concepts and expected outputs identified\n  * [ ] Required facts/data points listed\n* **Stage 2 (Retrieval)**:\n  * [ ] Meets quality standards (see above)\n  * [ ] If not met: execute query iteration\n* **Stage 3 (Generation)**:\n  * [ ] Each conclusion has at least one direct evidence source\n  * [ ] State assumptions/uncertainties\n  * [ ] Provide next-step suggestions or experiment/retrieval plans\n  * [ ] Final length and depth match user expectations (comply with word count/format if specified)\n---\n## Core Principles\n1. **Strict reliance on the knowledge base**: answers must be **fully bounded** by the content retrieved from the knowledge base.\n2. **No fabrication**: do not generate, infer, or create information that is not explicitly present in the knowledge base.\n3. **Accuracy first**: prefer incompleteness over inaccurate content.\n4. **Output format**:\n   * Hierarchically clear modular structure\n   * Logical grouping according to the MECE principle\n   * Professionally presented formatting\n   * Step-by-step cognitive guidance\n   * Reasonable use of headings and dividers for clarity\n   * *Italicize* key parameters\n   * **Bold** critical information\n5. **LaTeX formula requirements**:\n   * Inline formulas: start and end with `$`\n   * Block formulas: start and end with `$$`, each `$$` on its own line\n   * Block formula content must comply with LaTeX math syntax\n   * Verify formula correctness\n---\n## Additional Notes (Interaction & Failure Strategy)\n* If the knowledge base does not cover critical facts: explicitly inform the user (with sample wording)\n* For time-sensitive issues: enforce time filtering in the search request, and indicate the latest retrieval date in the answer.\n* Language requirement: answer in the user’s preferred language\n", "temperature": "0.1", "temperatureEnabled": true, "tools": [{"component_name": "Retrieval", "name": "Retrieval", "params": {"cross_languages": [], "description": "", "empty_response": "", "kb_ids": [], "keywords_similarity_weight": 0.7, "outputs": {"formalized_content": {"type": "string", "value": ""}}, "rerank_id": "", "similarity_threshold": 0.2, "top_k": 1024, "top_n": 8, "use_kg": false}}], "topPEnabled": false, "top_p": 0.75, "user_prompt": "", "visual_files_var": ""}}, "upstream": ["begin"]}, "Message:OrangeYearsShine": {"downstream": [], "obj": {"component_name": "Message", "params": {"content": ["{Agent:NewPumasLick@content}"]}}, "upstream": ["Agent:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "begin": {"downstream": ["Agent:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "obj": {"component_name": "<PERSON><PERSON>", "params": {"enablePrologue": true, "inputs": {}, "mode": "conversational", "prologue": "你好！ 我是你的助理，有什么可以帮到你的吗？"}}, "upstream": []}}, "globals": {"sys.conversation_turns": 0, "sys.files": [], "sys.query": "", "sys.user_id": ""}, "graph": {"edges": [{"data": {"isHovered": false}, "id": "xy-edge__beginstart-Agent:NewPumasLickend", "source": "begin", "sourceHandle": "start", "target": "Agent:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "targetHandle": "end"}, {"data": {"isHovered": false}, "id": "xy-edge__Agent:NewPumasLickstart-Message:OrangeYearsShineend", "markerEnd": "logo", "source": "Agent:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceHandle": "start", "style": {"stroke": "rgba(91, 93, 106, 1)", "strokeWidth": 1}, "target": "Message:OrangeYearsShine", "targetHandle": "end", "type": "buttonEdge", "zIndex": 1001}, {"data": {"isHovered": false}, "id": "xy-edge__Agent:NewPumasLicktool-Tool:AllBirdsNailend", "selected": false, "source": "Agent:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceHandle": "tool", "target": "Tool:AllBirdsNail", "targetHandle": "end"}], "nodes": [{"data": {"form": {"enablePrologue": true, "inputs": {}, "mode": "conversational", "prologue": "你好！ 我是你的助理，有什么可以帮到你的吗？"}, "label": "<PERSON><PERSON>", "name": "begin"}, "dragging": false, "id": "begin", "measured": {"height": 48, "width": 200}, "position": {"x": -9.569875358221438, "y": 205.84018385864917}, "selected": false, "sourcePosition": "left", "targetPosition": "right", "type": "beginNode"}, {"data": {"form": {"content": ["{Agent:NewPumasLick@content}"]}, "label": "Message", "name": "Response"}, "dragging": false, "id": "Message:OrangeYearsShine", "measured": {"height": 56, "width": 200}, "position": {"x": 734.4061285881053, "y": 199.9706031723009}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "messageNode"}, {"data": {"form": {"delay_after_error": 1, "description": "", "exception_comment": "", "exception_default_value": "", "exception_goto": [], "exception_method": null, "frequencyPenaltyEnabled": false, "frequency_penalty": 0.5, "llm_id": "qwen3-235b-a22b-instruct-2507@Tongyi-Qianwen", "maxTokensEnabled": true, "max_retries": 3, "max_rounds": 3, "max_tokens": 128000, "mcp": [], "message_history_window_size": 12, "outputs": {"content": {"type": "string", "value": ""}}, "parameter": "Precise", "presencePenaltyEnabled": false, "presence_penalty": 0.5, "prompts": [{"content": "# User Query\n {sys.query}", "role": "user"}], "sys_prompt": "## Role & Task\nYou are a **“Knowledge Base Retrieval Q\\&A Agent”** whose goal is to break down the user’s question into retrievable subtasks, and then produce a multi-source-verified, structured, and actionable research report using the internal knowledge base.\n## Execution Framework (Detailed Steps & Key Points)\n1. **Assessment & Decomposition**\n   * Actions:\n     * Automatically extract: main topic, subtopics, entities (people/organizations/products/technologies), time window, geographic/business scope.\n     * Output as a list: N facts/data points that must be collected (*N* ranges from 5–20 depending on question complexity).\n2. **Query Type Determination (Rule-Based)**\n   * Example rules:\n     * If the question involves a single issue but requests “method comparison/multiple explanations” → use **depth-first**.\n     * If the question can naturally be split into ≥3 independent sub-questions → use **breadth-first**.\n     * If the question can be answered by a single fact/specification/definition → use **simple query**.\n3. **Research Plan Formulation**\n   * Depth-first: define 3–5 perspectives (methodology/stakeholders/time dimension/technical route, etc.), assign search keywords, target document types, and output format for each perspective.\n   * Breadth-first: list subtasks, prioritize them, and assign search terms.\n   * Simple query: directly provide the search sentence and required fields.\n4. **Retrieval Execution**\n   * After retrieval: perform coverage check (does it contain the key facts?) and quality check (source diversity, authority, latest update time).\n   * If standards are not met, automatically loop: rewrite queries (synonyms/cross-domain terms) and retry ≤3 times, or flag as requiring external search.\n5. **Integration & Reasoning**\n   * Build the answer using a **fact–evidence–reasoning** chain. For each conclusion, attach 1–2 strongest pieces of evidence.\n---\n## Quality Gate Checklist (Verify at Each Stage)\n* **Stage 1 (Decomposition)**:\n  * [ ] Key concepts and expected outputs identified\n  * [ ] Required facts/data points listed\n* **Stage 2 (Retrieval)**:\n  * [ ] Meets quality standards (see above)\n  * [ ] If not met: execute query iteration\n* **Stage 3 (Generation)**:\n  * [ ] Each conclusion has at least one direct evidence source\n  * [ ] State assumptions/uncertainties\n  * [ ] Provide next-step suggestions or experiment/retrieval plans\n  * [ ] Final length and depth match user expectations (comply with word count/format if specified)\n---\n## Core Principles\n1. **Strict reliance on the knowledge base**: answers must be **fully bounded** by the content retrieved from the knowledge base.\n2. **No fabrication**: do not generate, infer, or create information that is not explicitly present in the knowledge base.\n3. **Accuracy first**: prefer incompleteness over inaccurate content.\n4. **Output format**:\n   * Hierarchically clear modular structure\n   * Logical grouping according to the MECE principle\n   * Professionally presented formatting\n   * Step-by-step cognitive guidance\n   * Reasonable use of headings and dividers for clarity\n   * *Italicize* key parameters\n   * **Bold** critical information\n5. **LaTeX formula requirements**:\n   * Inline formulas: start and end with `$`\n   * Block formulas: start and end with `$$`, each `$$` on its own line\n   * Block formula content must comply with LaTeX math syntax\n   * Verify formula correctness\n---\n## Additional Notes (Interaction & Failure Strategy)\n* If the knowledge base does not cover critical facts: explicitly inform the user (with sample wording)\n* For time-sensitive issues: enforce time filtering in the search request, and indicate the latest retrieval date in the answer.\n* Language requirement: answer in the user’s preferred language\n", "temperature": "0.1", "temperatureEnabled": true, "tools": [{"component_name": "Retrieval", "name": "Retrieval", "params": {"cross_languages": [], "description": "", "empty_response": "", "kb_ids": [], "keywords_similarity_weight": 0.7, "outputs": {"formalized_content": {"type": "string", "value": ""}}, "rerank_id": "", "similarity_threshold": 0.2, "top_k": 1024, "top_n": 8, "use_kg": false}}], "topPEnabled": false, "top_p": 0.75, "user_prompt": "", "visual_files_var": ""}, "label": "Agent", "name": "Knowledge Base Agent"}, "dragging": false, "id": "Agent:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "measured": {"height": 84, "width": 200}, "position": {"x": 347.00048227952215, "y": 186.49109364794631}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "agentNode"}, {"data": {"form": {"description": "This is an agent for a specific task.", "user_prompt": "This is the order you need to send to the agent."}, "label": "Tool", "name": "flow.tool_10"}, "dragging": false, "id": "Tool:AllBirdsNail", "measured": {"height": 48, "width": 200}, "position": {"x": 220.24819746977118, "y": 403.31576836482583}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "toolNode"}]}, "history": [], "memory": [], "messages": [], "path": [], "retrieval": []}, "avatar": "data:image/png;base64,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"}