#!/bin/bash

# RAGFlow 文件上传限制修复脚本
# 解决8个文件8MB限制问题

echo "🚀 RAGFlow 文件上传限制修复脚本"
echo "=================================="

# 检查是否在正确的目录
if [ ! -f "docker/docker-compose.yml" ]; then
    echo "❌ 错误：请在RAGFlow项目根目录下运行此脚本"
    echo "💡 当前目录：$(pwd)"
    echo "💡 请执行：cd /path/to/ragflow-0.20.3"
    exit 1
fi

# 显示当前目录
echo "📍 当前目录：$(pwd)"

echo "📋 当前修改内容："
echo "- 文件数量限制：8个 → 50个"
echo "- 单文件大小限制：8MB → 128MB"
echo "- 总上传限制：64MB → 6.4GB"
echo ""

# 询问用户是否继续
read -p "是否继续执行修复？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ 操作已取消"
    exit 1
fi

echo "🔧 开始修复..."

# 1. 停止服务
echo "1️⃣ 停止RAGFlow服务..."
docker compose -f docker/docker-compose-gpu.yml down

# 2. 清理缓存
echo "2️⃣ 清理Docker缓存..."
docker system prune -f

# 3. 重新构建并启动
echo "3️⃣ 重新构建并启动服务..."
docker compose -f docker/docker-compose-gpu.yml up -d --build

# 4. 等待服务启动
echo "4️⃣ 等待服务启动..."
sleep 30

# 5. 检查服务状态
echo "5️⃣ 检查服务状态..."
docker compose -f docker/docker-compose-gpu.yml ps

echo ""
echo "✅ 修复完成！"
echo ""
echo "📝 验证步骤："
echo "1. 打开浏览器访问 http://localhost:801"
echo "2. 进入知识库 → 数据集 → 上传文件"
echo "3. 查看是否显示'50个文件，每个128MB'"
echo "4. 进入知识库 → 设置 → 配置"
echo "5. 查看权限部分是否有'只有我'和'团队'选项"
echo ""
echo "🔄 如果仍显示旧限制，请："
echo "- 清除浏览器缓存（Ctrl+Shift+R）"
echo "- 或使用无痕模式访问"
echo ""
echo "📞 如有问题，请检查 DEPLOYMENT_GUIDE.md 文档"
