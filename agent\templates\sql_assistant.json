{"id": 17, "title": "SQL Assistant", "description": "SQL Assistant is an AI-powered tool that lets business users turn plain-English questions into fully formed SQL queries. Simply type your question (e.g., “Show me last quarter’s top 10 products by revenue”) and SQL Assistant generates the exact SQL, runs it against your database, and returns the results in seconds. ", "canvas_type": "Marketing", "dsl": {"components": {"Agent:WickedGoatsDivide": {"downstream": ["ExeSQL:TiredShirtsPull"], "obj": {"component_name": "Agent", "params": {"delay_after_error": 1, "description": "", "exception_default_value": "", "exception_goto": [], "exception_method": "", "frequencyPenaltyEnabled": false, "frequency_penalty": 0.7, "llm_id": "qwen-max@Tongyi-Qianwen", "maxTokensEnabled": false, "max_retries": 3, "max_rounds": 5, "max_tokens": 256, "mcp": [], "message_history_window_size": 12, "outputs": {"content": {"type": "string", "value": ""}}, "presencePenaltyEnabled": false, "presence_penalty": 0.4, "prompts": [{"content": "User's query: {sys.query}\n\nSchema: {Retrieval:HappyTiesFilm@formalized_content}\n\nSamples about question to SQL: {Retrieval:SmartNewsHammer@formalized_content}\n\nDescription about meanings of tables and files: {Retrieval:SweetDancersAppear@formalized_content}", "role": "user"}], "sys_prompt": "### ROLE\nYou are a Text-to-SQL assistant.  \nGiven a relational database schema and a natural-language request, you must produce a **single, syntactically-correct MySQL query** that answers the request.  \nReturn **nothing except the SQL statement itself**—no code fences, no commentary, no explanations, no comments, no trailing semicolon if not required.\n\n\n###  EXAMPLES  \n-- Example 1  \nUser: List every product name and its unit price.  \nSQL:\nSELECT name, unit_price FROM Products;\n\n-- Example 2  \nUser: Show the names and emails of customers who placed orders in January 2025.  \nSQL:\nSELECT DISTINCT c.name, c.email\nFROM Customers c\nJOIN Orders o ON o.customer_id = c.id\nWHERE o.order_date BETWEEN '2025-01-01' AND '2025-01-31';\n\n-- Example 3  \nUser: How many orders have a status of \"Completed\" for each month in 2024?  \nSQL:\nSELECT DATE_FORMAT(order_date, '%Y-%m') AS month,\n       COUNT(*) AS completed_orders\nFROM Orders\nWHERE status = 'Completed'\n  AND YEAR(order_date) = 2024\nGROUP BY month\nORDER BY month;\n\n-- Example 4  \nUser: Which products generated at least \\$10 000 in total revenue?  \nSQL:\nSELECT p.id, p.name, SUM(oi.quantity * oi.unit_price) AS revenue\nFROM Products p\nJOIN OrderItems oi ON oi.product_id = p.id\nGROUP BY p.id, p.name\nHAVING revenue >= 10000\nORDER BY revenue DESC;\n\n\n### OUTPUT GUIDELINES\n1. Think through the schema and the request.  \n2. Write **only** the final MySQL query.  \n3. Do **not** wrap the query in back-ticks or markdown fences.  \n4. Do **not** add explanations, comments, or additional text—just the SQL.", "temperature": 0.1, "temperatureEnabled": false, "tools": [], "topPEnabled": false, "top_p": 0.3, "user_prompt": "", "visual_files_var": ""}}, "upstream": ["Retrieval:HappyTiesFilm", "Retrieval:SmartNewsHammer", "Retrieval:SweetDancersAppear"]}, "ExeSQL:TiredShirtsPull": {"downstream": ["Message:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ttend"], "obj": {"component_name": "ExeSQL", "params": {"database": "", "db_type": "mysql", "host": "", "max_records": 1024, "outputs": {"formalized_content": {"type": "string", "value": ""}, "json": {"type": "Array<Object>", "value": []}}, "password": "20010812Yy!", "port": 3306, "sql": "Agent:WickedG<PERSON>sDivide@content", "username": "<EMAIL>"}}, "upstream": ["Agent:WickedGoatsDivide"]}, "Message:ShaggyMasksAttend": {"downstream": [], "obj": {"component_name": "Message", "params": {"content": ["{ExeSQL:TiredShirtsPull@formalized_content}"]}}, "upstream": ["ExeSQL:TiredShirtsPull"]}, "Retrieval:HappyTiesFilm": {"downstream": ["Agent:WickedGoatsDivide"], "obj": {"component_name": "Retrieval", "params": {"cross_languages": [], "empty_response": "", "kb_ids": ["ed31364c727211f0bdb2bafe6e7908e6"], "keywords_similarity_weight": 0.7, "outputs": {"formalized_content": {"type": "string", "value": ""}}, "query": "sys.query", "rerank_id": "", "similarity_threshold": 0.2, "top_k": 1024, "top_n": 8, "use_kg": false}}, "upstream": ["begin"]}, "Retrieval:SmartNewsHammer": {"downstream": ["Agent:WickedGoatsDivide"], "obj": {"component_name": "Retrieval", "params": {"cross_languages": [], "empty_response": "", "kb_ids": ["0f968106727311f08357bafe6e7908e6"], "keywords_similarity_weight": 0.7, "outputs": {"formalized_content": {"type": "string", "value": ""}}, "query": "sys.query", "rerank_id": "", "similarity_threshold": 0.2, "top_k": 1024, "top_n": 8, "use_kg": false}}, "upstream": ["begin"]}, "Retrieval:SweetDancersAppear": {"downstream": ["Agent:WickedGoatsDivide"], "obj": {"component_name": "Retrieval", "params": {"cross_languages": [], "empty_response": "", "kb_ids": ["4ad1f9d0727311f0827dbafe6e7908e6"], "keywords_similarity_weight": 0.7, "outputs": {"formalized_content": {"type": "string", "value": ""}}, "query": "sys.query", "rerank_id": "", "similarity_threshold": 0.2, "top_k": 1024, "top_n": 8, "use_kg": false}}, "upstream": ["begin"]}, "begin": {"downstream": ["Retrieval:HappyTiesFilm", "Retrieval:SmartNewsHammer", "Retrieval:SweetDancersAppear"], "obj": {"component_name": "<PERSON><PERSON>", "params": {"enablePrologue": true, "inputs": {}, "mode": "conversational", "prologue": "Hi! I'm your SQL assistant. What can I do for you?"}}, "upstream": []}}, "globals": {"sys.conversation_turns": 0, "sys.files": [], "sys.query": "", "sys.user_id": ""}, "graph": {"edges": [{"data": {"isHovered": false}, "id": "xy-edge__beginstart-Retrieval:HappyTiesFilmend", "source": "begin", "sourceHandle": "start", "target": "Retrieval:HappyTiesFilm", "targetHandle": "end"}, {"id": "xy-edge__beginstart-Retrieval:SmartNewsHammerend", "source": "begin", "sourceHandle": "start", "target": "Retrieval:SmartNewsHammer", "targetHandle": "end"}, {"data": {"isHovered": false}, "id": "xy-edge__beginstart-Retrieval:SweetDancersAppearend", "source": "begin", "sourceHandle": "start", "target": "Retrieval:SweetDancersAppear", "targetHandle": "end"}, {"data": {"isHovered": false}, "id": "xy-edge__Retrieval:HappyTiesFilmstart-Agent:WickedGoatsDivideend", "source": "Retrieval:HappyTiesFilm", "sourceHandle": "start", "target": "Agent:WickedGoatsDivide", "targetHandle": "end"}, {"data": {"isHovered": false}, "id": "xy-edge__Retrieval:SmartNewsHammerstart-Agent:WickedGoatsDivideend", "markerEnd": "logo", "source": "Retrieval:SmartNewsHammer", "sourceHandle": "start", "style": {"stroke": "rgba(91, 93, 106, 1)", "strokeWidth": 1}, "target": "Agent:WickedGoatsDivide", "targetHandle": "end", "type": "buttonEdge", "zIndex": 1001}, {"data": {"isHovered": false}, "id": "xy-edge__Retrieval:SweetDancersAppearstart-Agent:WickedGoatsDivideend", "markerEnd": "logo", "source": "Retrieval:SweetDancersAppear", "sourceHandle": "start", "style": {"stroke": "rgba(91, 93, 106, 1)", "strokeWidth": 1}, "target": "Agent:WickedGoatsDivide", "targetHandle": "end", "type": "buttonEdge", "zIndex": 1001}, {"data": {"isHovered": false}, "id": "xy-edge__Agent:WickedGoatsDividestart-ExeSQL:TiredShirtsPullend", "source": "Agent:WickedGoatsDivide", "sourceHandle": "start", "target": "ExeSQL:TiredShirtsPull", "targetHandle": "end"}, {"data": {"isHovered": false}, "id": "xy-edge__ExeSQL:TiredShirtsPullstart-Message:ShaggyMasksAttendend", "source": "ExeSQL:TiredShirtsPull", "sourceHandle": "start", "target": "Message:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ttend", "targetHandle": "end"}], "nodes": [{"data": {"form": {"enablePrologue": true, "inputs": {}, "mode": "conversational", "prologue": "Hi! I'm your SQL assistant. What can I do for you?"}, "label": "<PERSON><PERSON>", "name": "begin"}, "id": "begin", "measured": {"height": 48, "width": 200}, "position": {"x": 50, "y": 200}, "selected": false, "sourcePosition": "left", "targetPosition": "right", "type": "beginNode"}, {"data": {"form": {"cross_languages": [], "empty_response": "", "kb_ids": ["ed31364c727211f0bdb2bafe6e7908e6"], "keywords_similarity_weight": 0.7, "outputs": {"formalized_content": {"type": "string", "value": ""}}, "query": "sys.query", "rerank_id": "", "similarity_threshold": 0.2, "top_k": 1024, "top_n": 8, "use_kg": false}, "label": "Retrieval", "name": "<PERSON><PERSON><PERSON>"}, "dragging": false, "id": "Retrieval:HappyTiesFilm", "measured": {"height": 96, "width": 200}, "position": {"x": 414, "y": 20.5}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "retrievalNode"}, {"data": {"form": {"cross_languages": [], "empty_response": "", "kb_ids": ["0f968106727311f08357bafe6e7908e6"], "keywords_similarity_weight": 0.7, "outputs": {"formalized_content": {"type": "string", "value": ""}}, "query": "sys.query", "rerank_id": "", "similarity_threshold": 0.2, "top_k": 1024, "top_n": 8, "use_kg": false}, "label": "Retrieval", "name": "Question to SQL"}, "dragging": false, "id": "Retrieval:SmartNewsHammer", "measured": {"height": 96, "width": 200}, "position": {"x": 406.5, "y": 175.5}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "retrievalNode"}, {"data": {"form": {"cross_languages": [], "empty_response": "", "kb_ids": ["4ad1f9d0727311f0827dbafe6e7908e6"], "keywords_similarity_weight": 0.7, "outputs": {"formalized_content": {"type": "string", "value": ""}}, "query": "sys.query", "rerank_id": "", "similarity_threshold": 0.2, "top_k": 1024, "top_n": 8, "use_kg": false}, "label": "Retrieval", "name": "Database Description"}, "dragging": false, "id": "Retrieval:SweetDancersAppear", "measured": {"height": 96, "width": 200}, "position": {"x": 403.5, "y": 328}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "retrievalNode"}, {"data": {"form": {"delay_after_error": 1, "description": "", "exception_default_value": "", "exception_goto": [], "exception_method": "", "frequencyPenaltyEnabled": false, "frequency_penalty": 0.7, "llm_id": "qwen-max@Tongyi-Qianwen", "maxTokensEnabled": false, "max_retries": 3, "max_rounds": 5, "max_tokens": 256, "mcp": [], "message_history_window_size": 12, "outputs": {"content": {"type": "string", "value": ""}}, "presencePenaltyEnabled": false, "presence_penalty": 0.4, "prompts": [{"content": "User's query: {sys.query}\n\nSchema: {Retrieval:HappyTiesFilm@formalized_content}\n\nSamples about question to SQL: {Retrieval:SmartNewsHammer@formalized_content}\n\nDescription about meanings of tables and files: {Retrieval:SweetDancersAppear@formalized_content}", "role": "user"}], "sys_prompt": "### ROLE\nYou are a Text-to-SQL assistant.  \nGiven a relational database schema and a natural-language request, you must produce a **single, syntactically-correct MySQL query** that answers the request.  \nReturn **nothing except the SQL statement itself**—no code fences, no commentary, no explanations, no comments, no trailing semicolon if not required.\n\n\n###  EXAMPLES  \n-- Example 1  \nUser: List every product name and its unit price.  \nSQL:\nSELECT name, unit_price FROM Products;\n\n-- Example 2  \nUser: Show the names and emails of customers who placed orders in January 2025.  \nSQL:\nSELECT DISTINCT c.name, c.email\nFROM Customers c\nJOIN Orders o ON o.customer_id = c.id\nWHERE o.order_date BETWEEN '2025-01-01' AND '2025-01-31';\n\n-- Example 3  \nUser: How many orders have a status of \"Completed\" for each month in 2024?  \nSQL:\nSELECT DATE_FORMAT(order_date, '%Y-%m') AS month,\n       COUNT(*) AS completed_orders\nFROM Orders\nWHERE status = 'Completed'\n  AND YEAR(order_date) = 2024\nGROUP BY month\nORDER BY month;\n\n-- Example 4  \nUser: Which products generated at least \\$10 000 in total revenue?  \nSQL:\nSELECT p.id, p.name, SUM(oi.quantity * oi.unit_price) AS revenue\nFROM Products p\nJOIN OrderItems oi ON oi.product_id = p.id\nGROUP BY p.id, p.name\nHAVING revenue >= 10000\nORDER BY revenue DESC;\n\n\n### OUTPUT GUIDELINES\n1. Think through the schema and the request.  \n2. Write **only** the final MySQL query.  \n3. Do **not** wrap the query in back-ticks or markdown fences.  \n4. Do **not** add explanations, comments, or additional text—just the SQL.", "temperature": 0.1, "temperatureEnabled": false, "tools": [], "topPEnabled": false, "top_p": 0.3, "user_prompt": "", "visual_files_var": ""}, "label": "Agent", "name": "SQL Generator "}, "dragging": false, "id": "Agent:WickedGoatsDivide", "measured": {"height": 84, "width": 200}, "position": {"x": 981, "y": 174}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "agentNode"}, {"data": {"form": {"database": "", "db_type": "mysql", "host": "", "max_records": 1024, "outputs": {"formalized_content": {"type": "string", "value": ""}, "json": {"type": "Array<Object>", "value": []}}, "password": "20010812Yy!", "port": 3306, "sql": "Agent:WickedG<PERSON>sDivide@content", "username": "<EMAIL>"}, "label": "ExeSQL", "name": "ExeSQL"}, "dragging": false, "id": "ExeSQL:TiredShirtsPull", "measured": {"height": 56, "width": 200}, "position": {"x": 1211.5, "y": 212.5}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "ragNode"}, {"data": {"form": {"content": ["{ExeSQL:TiredShirtsPull@formalized_content}"]}, "label": "Message", "name": "Message"}, "dragging": false, "id": "Message:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ttend", "measured": {"height": 56, "width": 200}, "position": {"x": 1447.3125, "y": 181.5}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "messageNode"}, {"data": {"form": {"text": "Searches for relevant database creation statements.\n\nIt should label with a knowledgebase to which the schema is dumped in. You could use \" General \" as parsing method, \" 2 \" as chunk size and \" ; \"  as delimiter."}, "label": "Note", "name": "Note <PERSON><PERSON>a"}, "dragHandle": ".note-drag-handle", "dragging": false, "height": 188, "id": "Note:ThickClubsFloat", "measured": {"height": 188, "width": 392}, "position": {"x": 689, "y": -180.31251144409183}, "resizing": false, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "noteNode", "width": 392}, {"data": {"form": {"text": "Searches for samples about question to SQL. \n\nYou could use \" Q&A \" as parsing method.\n\nPlease check this dataset:\nhttps://huggingface.co/datasets/InfiniFlow/text2sql"}, "label": "Note", "name": "Note: Question to SQL"}, "dragHandle": ".note-drag-handle", "dragging": false, "height": 154, "id": "Note:ElevenLionsJoke", "measured": {"height": 154, "width": 345}, "position": {"x": 693.5, "y": 138}, "resizing": false, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "noteNode", "width": 345}, {"data": {"form": {"text": "Searches for description about meanings of tables and fields.\n\nYou could use \" General \" as parsing method, \" 2 \" as chunk size and \" ### \"  as delimiter."}, "label": "Note", "name": "Note: Database Description"}, "dragHandle": ".note-drag-handle", "dragging": false, "height": 158, "id": "Note:ManyRosesTrade", "measured": {"height": 158, "width": 408}, "position": {"x": 691.5, "y": 435.69736389555317}, "resizing": false, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "noteNode", "width": 408}, {"data": {"form": {"text": "The Agent learns which tables may be available based on the responses from three knowledge bases and converts the user's input into SQL statements."}, "label": "Note", "name": "Note: SQL Generator"}, "dragHandle": ".note-drag-handle", "dragging": false, "height": 132, "id": "Note:RudeHousesInvite", "measured": {"height": 132, "width": 383}, "position": {"x": 1106.9254833678003, "y": 290.5891036507015}, "resizing": false, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "noteNode", "width": 383}, {"data": {"form": {"text": "Connect to your database to execute SQL statements."}, "label": "Note", "name": "Note: SQL Executor"}, "dragHandle": ".note-drag-handle", "dragging": false, "id": "Note:HungryBatsLay", "measured": {"height": 136, "width": 255}, "position": {"x": 1185, "y": -30}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "noteNode"}]}, "history": [], "messages": [], "path": [], "retrieval": []}, "avatar": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAcFBQYFBAcGBQYIBwcIChELCgkJChUPEAwRGBUaGRgVGBcbHichGx0lHRcYIi4iJSgpKywrGiAvMy8qMicqKyr/2wBDAQcICAoJChQLCxQqHBgcKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKir/wAARCAAwADADAREAAhEBAxEB/8QAGgAAAwEBAQEAAAAAAAAAAAAABQYHBAMAAf/EADIQAAEDAwMCBAMHBQAAAAAAAAECAwQFESEABjESEyJBUYEUYXEHFSNSkaGxMjNictH/xAAZAQADAQEBAAAAAAAAAAAAAAACAwQBAAX/xAAlEQACAgICAgEEAwAAAAAAAAABAgARAyESMQRBEyIycYFCkbH/2gAMAwEAAhEDEQA/AKHt2DGpNHXDLrZdWtSrIub39tZ5GbGwPA+pmDFkX7x7idvra85xqQaFNkxUTVIVJQzf8QpBFjbgEenNs681MnA9WJ6fEOKJoxVpSpFLTCo6KEZlTlLcQBIJS20hAv1D1ve+qPk52b0IsYuIGtyt7ZkVVNP+H3A5GdlN2u7GQUBSfmkk8cXH10tmLD6Yl0CG5qmTXBMZiQEMuvupUoKdc6UeEi4FsqOeBxrsKnv1AY+hJ2l5yfu6qQ6/UZtPDRHZ+Eldpsqz1hSrXJGLXwRxqxUQizFs7galPYUFDKT+h15oMuImspQpFiL+2i1A3A1bgxmixUgwlT8ZfgJ/y8P8HXdRuPZoxaqtfkQKbKqF03jtEoDeFKV1lNgfK4H764XfccVUgipvdiwKpFaXMLklFg4juuqV0m3Izg/MaEZCDYMScYqiJOd6xmqfUVfBJcWwtHV1Elfi87k51ViyhrsxL4ivQj1KrFZjTGjTJ8aShdyph5SUqFhwPzX9jpC0dXUqZK3ViHNq7oNaVJjz2Vw5LCrdKknpULZyfMf801MfI1e5NmpAGHUL12EZNFWWlhXSUuWHKgk3xomwEDuDhzLysySU9EndEVyIz3GmxJR+KpBIdCLlRHn/AFEjjIF9AMJlZ8gLZ/qUiJSg1Tu0HO4plFj4FC1h9NYfHIU7kwzgnqCJlKLiCO2s6hKytWiPJoFdfnLW7HS0or6bqXbjg2AI99XjAa3NPlL6jFTduOR5sd1+oyfjQMONqI7QOMA4V7/pqjHjC9SLNn56I1HiqrqTUKM0hbq2lpst5CQSST54xjSPJbICOHUhawISiRQ02T2Uq6AAkqFj/GquJQks1iEr/INLU82bploKSFXusG9xfjHofXQuQUNRoQqQT0ZwVEST5687iZWGgpDsebNbaTDfKVL/ALnbQU/UkKNhjXpFt0BJBVXe/wAGGG6YMlvvNkjlBGmKeJimHIVc0TY89akCKspT28C5BKgDyR7fvrCFI+q/1DQsvVfudYcVyKw49KU6tZyQbmwHFhrOKr9s0uz0CAIpbr3RKo1Rbh02C4HJISp2ZIz0pJ8IQk5Nr/QXznSX6NSnGAwHI/gD/TM+3vtAj1arJpcpgtPdPSH0kFt5wDxAWOOLgamIAFwijCfD927N2tGXuNxlK2W0occUhJWpR+QzzrPjc+pvyqT3Ftf2zbObf7YYecb6CrrDAGfy20wYMkA5Vjbtev7b3nEcXRela27d1ogoWi/rnQsjrqZzHdwzKoKUsqWz3mOnJUlZJt8uokD621w+RdzgynUkUpoUafPZXMnSHlrKluyX1Eug8XF7GwxbgWxrubMO5WmNRsCKtLfcY3rAU0nIltkBP+w0X8Jjdz//2Q=="}