# RAGFlow 知识库权限设置修复

## 问题描述
RAGFlow 0.20.3 版本中，新版本的知识库设置页面（`/dataset/setting/:id`）缺少权限设置选项，导致用户无法为团队成员设置知识库访问权限。

## 问题原因
新版本的 `GeneralForm` 组件中没有渲染 `permission` 字段，虽然：
1. 数据库模型中有 `permission` 字段
2. form-schema 中定义了 `permission` 字段
3. 翻译文件中有相关翻译

## 解决方案
已修复 `web/src/pages/dataset/setting/general-form.tsx` 文件：

### 修改内容：
1. **添加 RadioGroup 组件导入**
2. **在表单中添加权限设置字段**
3. **在保存时包含 permission 参数**

### 修改后的效果：
- 在 General 选项卡中会显示"权限"设置
- 用户可以选择"只有我"或"团队"
- 保存时会正确提交权限设置

## 使用方法
修复后，用户可以通过以下步骤设置知识库权限：

1. 进入知识库页面
2. 点击"设置"选项卡
3. 在"General"选项卡中找到"权限"设置
4. 选择"只有我"或"团队"
5. 点击"保存"

## 注意事项
- 聊天助手（Dialog）模型中确实没有 permission 字段，无法直接设置团队权限
- Agent（智能体）支持团队权限设置
- 知识库权限设置为"团队"后，所有团队成员都可以操作该知识库

## 验证方法
1. 重新启动前端服务
2. 访问任意知识库的设置页面
3. 确认在 General 选项卡中能看到权限设置选项
4. 测试权限设置的保存功能
