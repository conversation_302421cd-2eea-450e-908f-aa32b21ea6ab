# RAGFlow 部署指南

## 🎯 修改完成清单

### ✅ 1. Docker 端口配置
- **docker/docker-compose.yml**: 端口 80→801, 443→4433
- **docker/docker-compose-gpu.yml**: 端口 80→801, 443→4433

### ✅ 2. Docker 镜像配置
- **docker/.env**: 使用完整版镜像 `infiniflow/ragflow:v0.20.3`（非slim版）

### ✅ 3. 文件上传限制提升
- **前端限制**: 50个文件，每个128MB
- **后端限制**: 单文件128MB
- **Nginx配置**: client_max_body_size 128M
- **修改文件**:
  - `web/src/components/file-upload-modal/index.tsx`
  - `web/src/components/file-upload-dialog/index.tsx`
  - `web/src/locales/zh.ts`
  - `web/src/locales/en.ts`

### ✅ 4. 权限设置功能修复
- **知识库权限**: 已添加"只有我"/"团队"选项
- **Agent权限**: 原本就支持团队权限

## 🚀 部署步骤

### 1. 环境准备
```bash
# 确保系统已安装 Docker 和 Docker Compose
docker --version
docker-compose --version

# 确保有足够的磁盘空间（建议至少20GB）
df -h
```

### 2. 克隆并进入项目目录
```bash
cd /path/to/ragflow-0.20.3
```

### 3. 启动服务
```bash
# 使用GPU版本启动（推荐）
docker compose -f docker/docker-compose-gpu.yml up -d

# 或使用CPU版本启动
docker compose -f docker/docker-compose.yml up -d
```

### 4. 验证部署
```bash
# 检查容器状态
docker ps

# 查看日志
docker compose -f docker/docker-compose-gpu.yml logs -f ragflow

# 等待服务完全启动（通常需要2-5分钟）
```

### 5. 访问服务
- **主要访问地址**: http://your-server-ip:801
- **HTTPS访问**: https://your-server-ip:4433
- **API端口**: http://your-server-ip:9380

## 🔧 功能验证

### 1. 文件上传测试
1. 登录系统
2. 创建知识库
3. 尝试上传多个文件（最多50个，每个最大128MB）
4. 验证上传成功

### 2. 权限设置测试
1. **知识库权限**:
   - 进入知识库设置 → General选项卡
   - 找到"权限"设置
   - 选择"团队"选项
   - 保存设置

2. **Agent权限**:
   - 创建或编辑Agent
   - 在设置中找到"权限"选项
   - 选择"团队"选项
   - 保存设置

### 3. 团队功能测试
1. 邀请团队成员
2. 验证成员可以访问设置为"团队"权限的知识库
3. 验证成员可以使用设置为"团队"权限的Agent

## 📋 配置详情

### 端口映射
| 服务 | 容器端口 | 主机端口 | 说明 |
|------|----------|----------|------|
| Web界面 | 80 | 801 | 主要访问端口 |
| HTTPS | 443 | 4433 | 安全访问端口 |
| API | 9380 | ${SVR_HTTP_PORT} | API服务端口 |

### 文件限制
| 项目 | 原限制 | 新限制 |
|------|--------|--------|
| 最大文件数 | 8个 | 50个 |
| 单文件大小 | 8MB | 128MB |
| 总上传限制 | 64MB | 6.4GB |

### 权限类型
| 权限 | 说明 | 适用范围 |
|------|------|----------|
| 只有我 | 仅创建者可访问 | 知识库、Agent |
| 团队 | 所有团队成员可访问 | 知识库、Agent |

## 🔧 问题解决

### 如果修改后仍显示旧的限制（8个文件8MB）

这通常是由于前端缓存或构建问题导致的，请按以下步骤解决：

#### 方法1：清除浏览器缓存
```bash
# 在浏览器中按 Ctrl+Shift+R (Windows/Linux) 或 Cmd+Shift+R (Mac)
# 或者在开发者工具中右键刷新按钮选择"清空缓存并硬性重新加载"
```

#### 方法2：重新构建前端（推荐）
```bash
# 进入项目目录
cd /path/to/ragflow-0.20.3

# 停止服务
docker compose -f docker/docker-compose-gpu.yml down

# 重新构建并启动
docker compose -f docker/docker-compose-gpu.yml up -d --build
```

#### 方法3：强制重新构建前端容器
```bash
# 删除前端相关容器和镜像
docker rmi infiniflow/ragflow:v0.20.3
docker system prune -f

# 重新启动
docker compose -f docker/docker-compose-gpu.yml up -d
```

### 验证修改是否生效

1. **文件上传限制验证**：
   - 进入知识库 → 数据集 → 上传文件
   - 查看上传界面是否显示"50个文件，每个128MB"
   - 尝试上传大于8MB的文件验证

2. **权限设置验证**：
   - 进入知识库 → 设置 → 配置
   - 查看权限部分是否有"只有我"和"团队"选项

## ⚠️ 注意事项

### 1. 系统要求
- **内存**: 建议至少8GB RAM
- **存储**: 建议至少20GB可用空间
- **GPU**: 如使用GPU版本，需要NVIDIA GPU + CUDA支持

### 2. 网络配置
- 确保防火墙开放端口801和4433
- 如需外网访问，配置相应的端口转发

### 3. 数据持久化
- 数据存储在Docker volumes中
- 定期备份重要数据
- 升级前务必备份数据

### 4. 前端缓存问题
- 修改前端代码后必须重新构建容器
- 浏览器可能缓存旧的JavaScript文件
- 建议使用无痕模式测试修改效果

### 4. 性能优化
- 根据实际使用情况调整Docker资源限制
- 监控系统资源使用情况
- 定期清理不需要的文件和数据

## 🔍 故障排除

### 常见问题
1. **端口冲突**: 检查801和4433端口是否被占用
2. **权限问题**: 确保Docker有足够权限访问文件系统
3. **内存不足**: 增加系统内存或调整Docker内存限制
4. **网络问题**: 检查防火墙和网络配置

### 日志查看
```bash
# 查看所有服务日志
docker compose -f docker/docker-compose-gpu.yml logs

# 查看特定服务日志
docker compose -f docker/docker-compose-gpu.yml logs ragflow

# 实时查看日志
docker compose -f docker/docker-compose-gpu.yml logs -f
```

## 🎉 部署完成

部署完成后，你将拥有：
- ✅ 支持50个文件，每个128MB的上传能力
- ✅ 完整的团队权限管理功能
- ✅ 稳定的RAGFlow v0.20.3完整版服务
- ✅ 自定义端口配置（801/4433）

现在可以开始使用RAGFlow进行知识库管理和AI对话了！
